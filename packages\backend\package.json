{"name": "figma-agent-backend", "version": "1.0.0", "description": "Backend service for Figma AI Chat Plugin", "main": "dist/index.js", "scripts": {"start": "node -r tsconfig-paths/register dist/index.js", "dev": "nodemon --exec ts-node -r tsconfig-paths/register src/index.ts", "build": "tsc && tsc-alias"}, "dependencies": {"@anthropic-ai/sdk": "^0.57.0", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "openai": "^4.96.0", "uuid": "^11.1.0", "ws": "^8.18.1"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20.10.5", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "tsc-alias": "^1.8.15", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}}