# packages
node_modules/
.pnp/
.pnp.js
yarn-error.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# dist
/build/
/dist/
packages/*/dist/
packages/*/build/

# env
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
packages/*/.env

# Figma related
/manifest.json
manifest.json
.figmarc
figma-keys.json


*.css.d.ts

# system files
.DS_Store
Thumbs.db
desktop.ini

# editor directories
.idea/
.vscode/
*.sublime-project
*.sublime-workspace
.project
.settings/

# logs
logs/
*.log

# temp
.temp/
.tmp/
.cache/

.turbo
docs/
.cursor/