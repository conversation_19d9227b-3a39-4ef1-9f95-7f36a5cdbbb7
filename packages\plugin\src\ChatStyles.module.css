/* packages/plugin/src/ChatStyles.module.css */

/* 连接状态指示器 */
.connectionStatus {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
  margin-bottom: 8px;
}

.statusDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
}

.statusText {
  font-size: 12px;
}

/* 不同状态的样式 */
.statusConnecting {
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
}

.statusConnecting .statusDot {
  background-color: #ffcc00;
  animation: blink 1s infinite;
}

.statusConnected {
  background-color: #e8f5e9;
  border: 1px solid #c8e6c9;
}

.statusConnected .statusDot {
  background-color: #4caf50;
}

.statusError {
  background-color: #ffebee;
  border: 1px solid #ffcdd2;
}

.statusError .statusDot {
  background-color: #f44336;
}

.statusDisconnected {
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
}

.statusDisconnected .statusDot {
  background-color: #9e9e9e;
}

/* 连接错误提示 */
.connectionError {
  margin: 16px;
  padding: 16px;
  text-align: center;
  background-color: #ffebee;
  border-radius: 8px;
  border: 1px solid #ffcdd2;
}

.errorText {
  margin-bottom: 12px;
  color: #c62828;
}

@keyframes blink {
  0% {
    opacity: 0.4;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.4;
  }
}

/* 消息气泡基础样式 */
.bubble {
  padding: 8px 12px;
  border-radius: 12px;
  max-width: 85%;
  margin-bottom: 10px;
  word-break: break-word;
  line-height: 1.5;
  font-size: 14px;
  user-select: text;
  cursor: text;
}

/* 用户消息样式 */
.user {
  align-self: flex-end;
  background-color: var(--figma-color-bg-brand);
  color: white;
  white-space: pre-wrap;
  word-break: break-word;
}

/* AI 消息样式 */
.ai {
  align-self: flex-start;
  background-color: var(--figma-color-bg-secondary);
  color: var(--figma-color-text);
  white-space: pre-wrap;
  word-break: break-word;

}

/* 流式消息样式 */
.streaming {
  border-left: 3px solid var(--figma-color-bg-brand);
  animation: pulseHighlight 1.5s infinite alternate;
}

/* 打字指示器样式 */
.typingIndicator {
  display: inline-block;
  margin-left: 4px;
  color: var(--figma-color-text-secondary);
  animation: blink 1s infinite;
}

@keyframes pulseHighlight {
  from {
    box-shadow: 0 0 0 rgba(24, 160, 251, 0);
  }
  to {
    box-shadow: 0 0 3px rgba(24, 160, 251, 0.5);
  }
}

/* 聊天历史容器样式 */
.chatHistory {
  flex-grow: 1; /* 占据所有可用垂直空间 */
  overflow-y: auto; /* 内容超出时显示滚动条 */
  display: flex;
  flex-direction: column;
  padding: 16px; /* 上下左右统一内边距 */
}

/* 加载指示器容器样式 */
.loadingContainer {
  align-self: flex-start;
  margin-top: 4px;
  margin-left: 12px;
  margin-bottom: 8px;
}

/* 插件整体布局容器 */
.pluginWrapper {
  display: flex;
  flex-direction: column;
  height: 100vh; /* 占据视口全部高度 */
  width: 100%;
  box-sizing: border-box;
  font-family: var(--figma-font-family, sans-serif);
  background-color: var(--figma-color-bg);
}

/* 输入区域样式 - 使用与inputContainer类似的样式 */
.inputArea {
  padding: 0;
  border-top: 1px solid var(--figma-color-border);
  background-color: var(--figma-color-bg);
  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.05);
  position: relative;
}

/* 输入区容器样式 - 作为定位参照物 */
.inputContainer {
  padding: 0; /* 移除内边距，由 textarea 和 button 控制 */
  border-top: 1px solid var(--figma-color-border);
  background-color: var(--figma-color-bg);
  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.05);
  position: relative; /* **重要**: 为内部绝对定位的按钮提供参照 */
  /* 高度由内部 textarea 决定 */
}

/* 自定义 Textarea 样式 */
.textarea {
  display: block; /* 确保是块级元素 */
  width: 100%;
  height: 100px; /* **固定高度**: 你可以调整这个值, e.g., 120px */
  min-height: 60px; /* 可选: 最小高度 */
  resize: none; /* 禁止用户调整大小 */
  overflow-y: auto; /* **启用垂直滚动** */
  border: none; /* 移除默认边框 */
  outline: none; /* 移除焦点轮廓 */
  background-color: var(--figma-color-bg); /* 匹配 Figma 背景 */
  color: var(--figma-color-text); /* 匹配 Figma 文本颜色 */
  font-family: inherit; /* 继承字体 */
  font-size: 14px; /* 匹配其他文本 */
  line-height: 1.5; /* 行高 */
  padding: 12px; /* 内部文本边距 */
  padding-right: 65px; /* **重要**: 右侧留出空间给按钮 (根据按钮大小调整) */
  padding-bottom: 40px; /* **重要**: 底部留出空间给按钮 (根据按钮大小调整) */
  box-sizing: border-box; /* padding 包含在 height/width 内 */
  cursor: text; /* 光标显示为文本输入样式 */
}

.textarea:disabled {
  background-color: var(--figma-color-bg-disabled);
  cursor: not-allowed;
}

/* 发送按钮样式 - 覆盖在右下角 */
.sendButtonOverlay {
  position: absolute !important; /* 绝对定位 */
  bottom: 10px !important; /* 距离父容器底部距离 */
  right: 10px !important; /* 距离父容器右侧距离 */
  z-index: 999 !important; /* 提高z-index确保在顶层 */
  /* 调整按钮大小和样式 */
  padding: 4px 10px !important;
  min-height: 28px !important;
  height: 28px !important;
  line-height: 20px !important;
  border-radius: 6px !important;
  font-size: 13px !important;
  background-color: var(
    --figma-color-bg-brand
  ) !important; /* 使用Figma品牌色 */
  color: white !important;
  border: none !important;
  cursor: pointer !important;
  display: block !important; /* 确保按钮显示为块级元素 */
  overflow: visible !important; /* 确保按钮不会被裁剪 */
}

.sendButtonOverlay:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  background-color: var(--figma-color-bg-disabled) !important;
  color: var(--figma-color-text-disabled) !important;
}

/* 输入状态指示器 */
.inputStatusIndicator {
  padding: 5px 12px;
  font-size: 12px;
  color: var(--figma-color-text-secondary);
  background-color: var(--figma-color-bg-secondary);
  border-top: 1px solid var(--figma-color-border);
  text-align: center;
}

/* 初始提示消息样式 */
.initialPrompt {
  color: var(--figma-color-text-secondary);
  margin: auto;
  padding: 12px;
  text-align: center;
  font-size: 13px;
}
